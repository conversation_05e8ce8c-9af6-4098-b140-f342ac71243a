import { describe, it, expect, beforeEach } from 'vitest';
import { formatTimestamp, getSenderName, formatMessage } from '../../src/chat/utils/formatUtils.js';
import { mockEnv } from '../setup.js';

describe('formatUtils', () => {
	let env;

	beforeEach(() => {
		env = { ...mockEnv };
	});

	describe('formatTimestamp', () => {
		it('should format timestamp correctly with default timezone', () => {
			const timestamp = 1640995200; // 2022-01-01 00:00:00 UTC
			const result = formatTimestamp(timestamp, env);
			
			expect(result).toContain('2022');
			expect(result).toContain('Jan');
			expect(result).toContain('Saturday');
		});

		it('should handle zero timestamp', () => {
			const result = formatTimestamp(0, env);
			expect(result).toContain('1970');
		});

		it('should handle undefined timestamp', () => {
			const result = formatTimestamp(undefined, env);
			expect(result).toContain('1970');
		});

		it('should use custom timezone when provided', () => {
			env.TIMEZONE = 'America/New_York';
			const timestamp = 1640995200; // 2022-01-01 00:00:00 UTC
			const result = formatTimestamp(timestamp, env);
			
			expect(result).toContain('2021'); // Should be Dec 31, 2021 in NY timezone
		});
	});

	describe('getSenderName', () => {
		it('should return bot name for assistant role', () => {
			const messageData = { role: 'assistant' };
			const result = getSenderName(messageData, env);
			
			expect(result).toBe('TestBot');
		});

		it('should return bot name for bot user', () => {
			const messageData = { from: { is_bot: true } };
			const result = getSenderName(messageData, env);
			
			expect(result).toBe('TestBot');
		});

		it('should return first name for regular user', () => {
			const messageData = { firstName: 'John' };
			const result = getSenderName(messageData, env);
			
			expect(result).toBe('John');
		});

		it('should return from.first_name when firstName not available', () => {
			const messageData = { from: { first_name: 'Jane' } };
			const result = getSenderName(messageData, env);
			
			expect(result).toBe('Jane');
		});

		it('should return username when names not available', () => {
			const messageData = { from: { username: 'johndoe' } };
			const result = getSenderName(messageData, env);
			
			expect(result).toBe('johndoe');
		});

		it('should return "User" as fallback', () => {
			const messageData = {};
			const result = getSenderName(messageData, env);
			
			expect(result).toBe('User');
		});

		it('should use default bot name when env.TELEGRAM_BOT_NAME not set', () => {
			delete env.TELEGRAM_BOT_NAME;
			const messageData = { role: 'assistant' };
			const result = getSenderName(messageData, env);
			
			expect(result).toBe('Bot');
		});
	});

	describe('formatMessage', () => {
		it('should format user message correctly', () => {
			const messageData = {
				text: 'Hello world',
				timestamp: 1640995200,
				from: { first_name: 'John' }
			};
			
			const result = formatMessage(messageData, env);
			
			expect(result).toContain('MESSAGE');
			expect(result).toContain('John');
			expect(result).toContain('Hello world');
			expect(result).toContain('2022');
		});

		it('should format bot message correctly', () => {
			const messageData = {
				text: 'Hello user',
				timestamp: 1640995200,
				role: 'assistant'
			};
			
			const result = formatMessage(messageData, env);
			
			expect(result).toContain('AI_RESPONSE');
			expect(result).toContain('TestBot');
			expect(result).toContain('Hello user');
		});

		it('should handle message with custom timestamp', () => {
			const messageData = {
				text: 'Test message',
				timestamp: 1640995200,
				from: { first_name: 'John' }
			};
			const customTimestamp = 'Custom Time';
			
			const result = formatMessage(messageData, env, customTimestamp);
			
			expect(result).toContain('Custom Time');
			expect(result).not.toContain('2022');
		});

		it('should handle message without text', () => {
			const messageData = {
				timestamp: 1640995200,
				from: { first_name: 'John' }
			};
			
			const result = formatMessage(messageData, env);
			
			expect(result).toContain('MESSAGE');
			expect(result).toContain('John');
			// Should not crash and should contain empty text
		});

		it('should use date field when timestamp not available', () => {
			const messageData = {
				text: 'Test message',
				date: 1640995200,
				from: { first_name: 'John' }
			};
			
			const result = formatMessage(messageData, env);
			
			expect(result).toContain('2022');
		});
	});
});
