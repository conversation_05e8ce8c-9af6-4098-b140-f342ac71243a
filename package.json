{"name": "harmony-chat", "version": "1.0.0", "private": true, "scripts": {"deploy": "wrangler deploy --env production", "dev": "wrangler dev", "dev:public": "wrangler dev --remote", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250628.0", "@eslint/js": "^9.29.0", "@vitest/ui": "^3.2.4", "eslint": "^9.29.0", "globals": "^16.2.0", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "vitest": "^3.2.4", "wrangler": "^4.21.2"}, "dependencies": {"@google/genai": "^1.5.1", "@langchain/core": "^0.3.58", "@langchain/google-common": "^0.2.12", "@langchain/google-gauth": "^0.2.12", "@langchain/google-genai": "^0.2.12", "@langchain/langgraph": "^0.3.3", "@langchain/openai": "^0.5.13", "@upstash/redis": "^1.35.0", "@upstash/vector": "^1.2.1", "hono": "^4.7.10", "langchain": "^0.3.28", "langfuse-langchain": "^3.37.6"}}