import { describe, it, expect, vi, beforeEach } from 'vitest';
import { checkBotMention, fetchJsonOrThrow } from '../../src/chat/telegramUtils.js';

describe('telegramUtils', () => {
	describe('checkBotMention', () => {
		const botUsername = 'test_bot';

		it('should return true when botUsername is not provided', () => {
			const webhookData = { message: { text: 'Hello' } };
			const result = checkBotMention(webhookData, null);
			
			expect(result).toBe(true);
		});

		it('should return true for private chat messages', () => {
			const webhookData = {
				message: {
					chat: { type: 'private' },
					text: 'Hello'
				}
			};
			
			const result = checkBotMention(webhookData, botUsername);
			expect(result).toBe(true);
		});

		it('should return true when bot is mentioned in text', () => {
			const webhookData = {
				message: {
					chat: { type: 'group' },
					text: 'Hello @test_bot how are you?'
				}
			};
			
			const result = checkBotMention(webhookData, botUsername);
			expect(result).toBe(true);
		});

		it('should return true when bot is mentioned case insensitively', () => {
			const webhookData = {
				message: {
					chat: { type: 'group' },
					text: 'Hello @TEST_BOT how are you?'
				}
			};
			
			const result = checkBotMention(webhookData, botUsername);
			expect(result).toBe(true);
		});

		it('should return false when bot is not mentioned in group chat', () => {
			const webhookData = {
				message: {
					chat: { type: 'group' },
					text: 'Hello everyone!'
				}
			};
			
			const result = checkBotMention(webhookData, botUsername);
			expect(result).toBe(false);
		});

		it('should handle callback_query messages', () => {
			const webhookData = {
				callback_query: {
					message: {
						chat: { type: 'private' },
						text: 'Button clicked'
					}
				}
			};
			
			const result = checkBotMention(webhookData, botUsername);
			expect(result).toBe(true);
		});

		it('should return false when no message or callback_query', () => {
			const webhookData = {};
			
			const result = checkBotMention(webhookData, botUsername);
			expect(result).toBe(false);
		});

		it('should handle partial mentions correctly', () => {
			const webhookData = {
				message: {
					chat: { type: 'group' },
					text: 'Hello @test_bot_other how are you?'
				}
			};
			
			const result = checkBotMention(webhookData, botUsername);
			expect(result).toBe(false);
		});
	});

	describe('fetchJsonOrThrow', () => {
		beforeEach(() => {
			vi.clearAllMocks();
		});

		it('should return JSON data on successful response', async () => {
			const mockResponse = {
				ok: true,
				status: 200,
				json: vi.fn().mockResolvedValue({ ok: true, data: 'test' })
			};
			
			global.fetch = vi.fn().mockResolvedValue(mockResponse);
			
			const result = await fetchJsonOrThrow('https://api.test.com', 'Test API');
			
			expect(result).toEqual({ ok: true, data: 'test' });
			expect(global.fetch).toHaveBeenCalledWith('https://api.test.com');
		});

		it('should throw error on HTTP error status', async () => {
			const mockResponse = {
				ok: false,
				status: 404,
				json: vi.fn()
			};
			
			global.fetch = vi.fn().mockResolvedValue(mockResponse);
			
			await expect(fetchJsonOrThrow('https://api.test.com', 'Test API'))
				.rejects.toThrow('Test API HTTP error! Status: 404');
		});

		it('should throw error when API returns ok: false', async () => {
			const mockResponse = {
				ok: true,
				status: 200,
				json: vi.fn().mockResolvedValue({ 
					ok: false, 
					description: 'Invalid request' 
				})
			};
			
			global.fetch = vi.fn().mockResolvedValue(mockResponse);
			
			await expect(fetchJsonOrThrow('https://api.test.com', 'Test API'))
				.rejects.toThrow('Test API API error! Description: Invalid request');
		});

		it('should handle fetch network errors', async () => {
			global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));
			
			await expect(fetchJsonOrThrow('https://api.test.com', 'Test API'))
				.rejects.toThrow('Network error');
		});

		it('should handle JSON parsing errors', async () => {
			const mockResponse = {
				ok: true,
				status: 200,
				json: vi.fn().mockRejectedValue(new Error('Invalid JSON'))
			};
			
			global.fetch = vi.fn().mockResolvedValue(mockResponse);
			
			await expect(fetchJsonOrThrow('https://api.test.com', 'Test API'))
				.rejects.toThrow('Invalid JSON');
		});
	});
});
