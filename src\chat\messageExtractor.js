import { checkBotMention } from './telegramUtils.js';

/**
 * Extracts relevant data from the Telegram webhook payload.
 */
export function extractMessageData(env, webhookData) {
	const message = webhookData.message || (webhookData.callback_query && webhookData.callback_query.message);
	const chatId = message?.chat?.id;
	let text = message?.text || '';
	const photo = message?.photo || [];
	const document = message?.document || {};
	const replyToMessage = message?.reply_to_message;
	const replyToPhoto = replyToMessage?.photo || [];
	const replyToDocument = replyToMessage?.document || {};

	// Use caption if the current message has media, otherwise use text
	if (photo.length > 0 || document.file_id) {
		text = message?.caption || '';
	}

	// Remove bot username mentions
	const botUsername = env.TELEGRAM_BOT_USERNAME;
	const mentionRegex = new RegExp(`@${botUsername}\\b`, 'gi');
	text = text.replace(mentionRegex, '').trim();

	const userId = message?.from?.id;
	const username = message?.from?.username || 'unknown';
	const firstName = message?.from?.first_name || '';
	const lastName = message?.from?.last_name || '';
	const messageId = message?.message_id;
	const messageDate = message?.date;
	const chatType = message?.chat?.type;
	const mediaGroupId = message?.media_group_id;

	return {
		message, // Original message object for convenience
		chatId,
		text,
		photo,
		document,
		userId,
		username,
		firstName,
		lastName,
		messageId,
		messageDate,
		chatType,
		mediaGroupId,
		replyToMessage,
		replyToPhoto,
		replyToDocument,
	};
}

/**
 * Determines if a message should be processed based on content and context.
 */
export function shouldProcessMessage(messageData, botUsername, webhookData) {
	// Destructure needed properties from messageData
	const { chatId, text, photo, document, chatType, mediaGroupId, replyToPhoto, replyToDocument } = messageData;

	if (!chatId) {
		console.log('Skipping processing: No valid chat ID.');
		return false;
	}

	// Skip media group messages without a caption (text)
	if (mediaGroupId && !text) {
		console.log(`Skipping processing for chat ${chatId}: Media group message without caption.`);
		return false;
	}

	// Check if there's any content to process (text, photo, document, or replied-to media)
	const hasContent = photo.length > 0 || document.file_id || text || replyToPhoto.length > 0 || replyToDocument.file_id;
	if (!hasContent) {
		console.log(`Skipping processing for chat ${chatId}: No text, photo, document, or replied-to media content.`);
		return false;
	}

	// Process messages with media (current or replied-to) OR messages in private chats regardless of mention
	if (photo.length > 0 || document.file_id || replyToPhoto.length > 0 || replyToDocument.file_id || chatType === 'private') {
		return true;
	}

	// For text-only messages in non-private chats, require a mention
	if (text && chatType !== 'private') {
		const isBotMentioned = checkBotMention(webhookData, botUsername);
		if (!isBotMentioned) {
			console.log(`Skipping processing for chat ${chatId}: Bot not mentioned in group chat.`);
			return false;
		}
		return true;
	}

	console.log(`Skipping processing for chat ${chatId}: Unhandled case.`);
	return false;
}
