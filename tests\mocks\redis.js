import { vi } from 'vitest';

// Mock Redis client for Upstash
export const mockRedis = {
	get: vi.fn(),
	set: vi.fn(),
	del: vi.fn(),
	exists: vi.fn(),
	lpush: vi.fn(),
	lrange: vi.fn(),
	ltrim: vi.fn(),
	expire: vi.fn(),
	incr: vi.fn(),
	decr: vi.fn(),
	hget: vi.fn(),
	hset: vi.fn(),
	hdel: vi.fn(),
	hgetall: vi.fn(),
};

// Mock the Redis.fromEnv method
export const Redis = {
	fromEnv: vi.fn(() => mockRedis),
};

// Mock the getRedisClient function
export const getRedisClient = vi.fn(() => mockRedis);
