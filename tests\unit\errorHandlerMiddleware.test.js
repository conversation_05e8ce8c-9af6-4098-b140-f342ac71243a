import { describe, it, expect, vi, beforeEach } from 'vitest';
import { errorHandlerMiddleware } from '../../src/middleware/errorHandler.js';
import { mockEnv } from '../setup.js';

// Mock sendTelegramError
vi.mock('../../src/telegram.js', () => ({
	sendTelegramError: vi.fn(),
}));

describe('errorHandlerMiddleware', () => {
	let mockContext;
	let mockNext;
	let consoleSpy;
	let consoleWarnSpy;

	beforeEach(() => {
		vi.clearAllMocks();
		mockNext = vi.fn();
		consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
		consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

		mockContext = {
			req: {
				path: '/test',
				method: 'GET',
			},
			res: {
				status: 200,
			},
			env: { ...mockEnv },
			json: vi.fn(),
		};
	});

	afterEach(() => {
		consoleSpy.mockRestore();
		consoleWarnSpy.mockRestore();
	});

	describe('successful request handling', () => {
		it('should proceed normally when no errors occur', async () => {
			mockNext.mockResolvedValue();

			await errorHandlerMiddleware(mockContext, mockNext);

			expect(mockNext).toHaveBeenCalledOnce();
			expect(mockContext.json).not.toHaveBeenCalled();
			expect(consoleSpy).not.toHaveBeenCalled();
		});

		it('should warn when no response status is set', async () => {
			mockContext.res.status = undefined;
			mockNext.mockResolvedValue();

			await errorHandlerMiddleware(mockContext, mockNext);

			expect(consoleWarnSpy).toHaveBeenCalledWith('No response set for path: /test, returning 404.');
		});
	});

	describe('error handling', () => {
		it('should catch and handle errors from next middleware', async () => {
			const error = new Error('Test error message');
			mockNext.mockRejectedValue(error);

			const result = await errorHandlerMiddleware(mockContext, mockNext);

			expect(consoleSpy).toHaveBeenCalledWith('Unhandled error caught in errorHandlerMiddleware:', {
				message: 'Test error message',
				stack: error.stack,
				path: '/test',
				method: 'GET',
			});

			const { sendTelegramError } = await import('../../src/telegram.js');
			expect(sendTelegramError).toHaveBeenCalledWith(mockContext.env, error, { path: '/test', method: 'GET' });

			expect(mockContext.json).toHaveBeenCalledWith(
				expect.objectContaining({
					error: true,
					details: 'Test error message',
					status: 500,
					stack: expect.any(String),
				}),
				500
			);
		});

		it('should use custom status code from error', async () => {
			const error = new Error('Not found');
			error.status = 404;
			mockNext.mockRejectedValue(error);

			await errorHandlerMiddleware(mockContext, mockNext);

			expect(mockContext.json).toHaveBeenCalledWith(
				expect.objectContaining({
					error: true,
					details: 'Not found',
					status: 404,
					stack: expect.any(String),
				}),
				404
			);
		});

		it('should use statusCode property if status not available', async () => {
			const error = new Error('Bad request');
			error.statusCode = 400;
			mockNext.mockRejectedValue(error);

			await errorHandlerMiddleware(mockContext, mockNext);

			expect(mockContext.json).toHaveBeenCalledWith(
				expect.objectContaining({
					error: true,
					details: 'Bad request',
					status: 400,
					stack: expect.any(String),
				}),
				400
			);
		});

		it('should default to 500 status when no status provided', async () => {
			const error = new Error('Generic error');
			mockNext.mockRejectedValue(error);

			await errorHandlerMiddleware(mockContext, mockNext);

			expect(mockContext.json).toHaveBeenCalledWith(
				expect.objectContaining({
					error: true,
					details: 'Generic error',
					status: 500,
					stack: expect.any(String),
				}),
				500
			);
		});

		it('should handle errors without message', async () => {
			const error = new Error();
			mockNext.mockRejectedValue(error);

			await errorHandlerMiddleware(mockContext, mockNext);

			expect(mockContext.json).toHaveBeenCalledWith(
				expect.objectContaining({
					error: true,
					details: 'An unexpected internal server error occurred.',
					status: 500,
					stack: expect.any(String),
				}),
				500
			);
		});

		it('should include stack trace in development mode', async () => {
			mockContext.env.DEV_MODE = 'true';
			const error = new Error('Dev error');
			error.stack = 'Error stack trace';
			mockNext.mockRejectedValue(error);

			await errorHandlerMiddleware(mockContext, mockNext);

			expect(mockContext.json).toHaveBeenCalledWith(
				{
					error: true,
					details: 'Dev error',
					status: 500,
					stack: 'Error stack trace',
				},
				500
			);
		});

		it('should not include stack trace in production mode', async () => {
			mockContext.env.DEV_MODE = 'false';
			const error = new Error('Prod error');
			error.stack = 'Error stack trace';
			mockNext.mockRejectedValue(error);

			await errorHandlerMiddleware(mockContext, mockNext);

			expect(mockContext.json).toHaveBeenCalledWith(
				{
					error: true,
					details: 'Prod error',
					status: 500,
				},
				500
			);
		});

		it('should handle errors without stack property', async () => {
			mockContext.env.DEV_MODE = 'true';
			const error = new Error('No stack error');
			delete error.stack;
			mockNext.mockRejectedValue(error);

			await errorHandlerMiddleware(mockContext, mockNext);

			expect(mockContext.json).toHaveBeenCalledWith(
				{
					error: true,
					details: 'No stack error',
					status: 500,
				},
				500
			);
		});
	});
});
