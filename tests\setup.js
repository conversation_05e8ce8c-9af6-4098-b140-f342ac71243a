import { vi } from 'vitest';

// Mock Cloudflare Workers environment
global.fetch = vi.fn();
global.Request = vi.fn();
global.Response = vi.fn();

// Mock environment variables for testing
export const mockEnv = {
	TELEGRAM_BOT_TOKEN: 'test-bot-token',
	HRMNY_BOT_TOKEN: 'test-hrmny-token',
	TELEGRAM_BOT_USERNAME: 'test_bot',
	TELEGRAM_BOT_NAME: 'TestBot',
	TELEGRAM_CHAT_ID: '123456789',
	TIMEZONE: 'UTC',
	DEV_MODE: 'true',
	PROTOCOL: 'https',
	HOSTNAME: 'test.workers.dev',
	// Redis mock env vars
	UPSTASH_REDIS_REST_URL: 'https://test-redis.upstash.io',
	UPSTASH_REDIS_REST_TOKEN: 'test-redis-token',
	// Vector store mock env vars
	UPSTASH_VECTOR_REST_URL: 'https://test-vector.upstash.io',
	UPSTASH_VECTOR_REST_TOKEN: 'test-vector-token',
	// AI service mock env vars
	GOOGLE_API_KEY: 'test-google-api-key',
	OPENAI_API_KEY: 'test-openai-api-key',
};

// Mock Cloudflare Workers execution context
export const mockExecutionContext = {
	waitUntil: vi.fn(),
	passThroughOnException: vi.fn(),
};

// Mock Hono context
export const createMockHonoContext = (overrides = {}) => ({
	env: { ...mockEnv, ...overrides.env },
	executionCtx: mockExecutionContext,
	req: {
		method: 'GET',
		url: 'https://test.workers.dev/',
		path: '/',
		header: vi.fn(),
		json: vi.fn(),
		param: vi.fn(),
		...overrides.req,
	},
	json: vi.fn(),
	text: vi.fn(),
	redirect: vi.fn(),
	...overrides,
});

// Mock Redis client
export const mockRedisClient = {
	get: vi.fn(),
	set: vi.fn(),
	del: vi.fn(),
	exists: vi.fn(),
	lpush: vi.fn(),
	lrange: vi.fn(),
	ltrim: vi.fn(),
	expire: vi.fn(),
};

// Mock Vector store client
export const mockVectorClient = {
	upsert: vi.fn(),
	query: vi.fn(),
	delete: vi.fn(),
};

// Setup global mocks before each test
beforeEach(() => {
	vi.clearAllMocks();
	
	// Reset fetch mock
	global.fetch.mockReset();
	
	// Mock successful fetch responses by default
	global.fetch.mockResolvedValue({
		ok: true,
		status: 200,
		json: vi.fn().mockResolvedValue({ ok: true }),
		text: vi.fn().mockResolvedValue(''),
	});
});

// Cleanup after each test
afterEach(() => {
	vi.restoreAllMocks();
});
