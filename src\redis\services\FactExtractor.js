import { callFastGenerativeAI } from '../../chat/geminiAI.js';

export class FactExtractor {
	constructor(env) {
		this.env = env;
	}

	async extractFacts(firstName, text, messageHistory) {
		const { EXTRACTION_PROMPT, EXTRACTION_SYSTEM_PROMPT } = await import('../../constants.js');

		const context = this._buildContext(messageHistory);
		const userMessage = this._formatUserMessage(firstName, text);
		const prompt = EXTRACTION_PROMPT.replace('{USER_MESSAGE}', userMessage).replace('{HISTORY}', context);

		const response = await this._callAI(prompt, EXTRACTION_SYSTEM_PROMPT);
		return this._parseFacts(response, firstName);
	}

	_buildContext(messageHistory) {
		if (!messageHistory.length) return '';

		return messageHistory
			.map((m) => {
				const messageText = m.text && typeof m.text === 'string' ? m.text : '';
				const fromName = m.from?.first_name || '';
				const lastName = m.from?.last_name ? ' ' + m.from.last_name : '';
				const username = m.from?.is_bot ? m.from.username : '';
				const timestamp = new Date(m.date * 1000).toLocaleString('en-US', { timeZone: this.env.TIMEZONE });

				return `> ${timestamp}. ${fromName}${lastName}${username}: ${messageText}`;
			})
			.join('\n');
	}

	_formatUserMessage(firstName, text) {
		const timestamp = new Date().toLocaleString('en-US', { timeZone: this.env.TIMEZONE });
		return `${timestamp}. ${firstName} said: ${text}`;
	}

	async _callAI(prompt, systemPrompt) {
		const config = {
			temperature: 0.25,
			systemInstruction: systemPrompt,
			inferenceProvider: 'cerebras',
		};

		const contents = [{ role: 'user', parts: [{ text: prompt }] }];
		return callFastGenerativeAI(this.env, config, contents);
	}

	_parseFacts(response, firstName) {
		if (!response) {
			console.warn(`[FactExtractor] No response from AI for user ${firstName}`);
			return [];
		}

		if (response.text.toUpperCase() === 'NO_FACTS_FOUND') {
			console.log(`[FactExtractor] No facts found for ${firstName}`);
			return [];
		}

		const facts = response.text
			.split('\n')
			.map((fact) => fact.replace(/^[-•*]\s*/, '').replace(/^\d+\.\s*/, ''))
			.filter((fact) => fact.length > 5);

		console.log(`[FactExtractor] Extracted ${facts.length} facts for ${firstName}`);
		return facts;
	}
}
