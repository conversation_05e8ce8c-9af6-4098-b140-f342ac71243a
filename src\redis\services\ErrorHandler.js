export class ErrorHandler {
	constructor() {
		this._errorTypes = null;
	}

	async _getErrorTypes() {
		if (!this._errorTypes) {
			this._errorTypes = (await import('../../constants.js')).FACT_EXTRACTION_ERRORS;
		}
		return this._errorTypes;
	}

	async createEmptyInputError(metrics) {
		const errorTypes = await this._getErrorTypes();
		const errorType = errorTypes?.PROCESSING_ERROR || 'PROCESSING_ERROR';

		return {
			success: false,
			error: 'Input text is empty',
			errorType,
			metrics: {
				...metrics,
				totalTime: 0,
				success: false,
				errorType,
				factsExtracted: 0,
				factsStored: 0,
			},
		};
	}

	async handleError(error, metrics) {
		const errorTypes = await this._getErrorTypes();
		const errorType = this._categorizeError(error, errorTypes);

		console.error(`[extractAndStoreFacts] Error for user ${metrics.userId}:`, {
			errorType,
			message: error.message,
			totalTime: `${metrics.totalTime}ms`,
		});

		return {
			success: false,
			error: error.message,
			errorType,
			metrics: {
				...metrics,
				success: false,
				errorType,
			},
		};
	}

	_categorizeError(error, errorTypes) {
		const message = error.message || '';
		
		if (message.includes('timeout')) return errorTypes.TIMEOUT_ERROR;
		if (message.includes('Redis') || message.includes('redis')) return errorTypes.REDIS_CONNECTION_ERROR;
		if (message.includes('Gemini') || message.includes('AI')) return errorTypes.AI_SERVICE_UNAVAILABLE;
		
		return errorTypes.PROCESSING_ERROR;
	}
}