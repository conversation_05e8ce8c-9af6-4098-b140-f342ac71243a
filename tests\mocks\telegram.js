import { vi } from 'vitest';

// Mock Telegram API responses
export const mockTelegramResponse = {
	ok: true,
	result: {
		message_id: 123,
		from: {
			id: 987654321,
			is_bot: true,
			first_name: 'TestBot',
			username: 'test_bot',
		},
		chat: {
			id: 123456789,
			type: 'private',
		},
		date: Math.floor(Date.now() / 1000),
		text: 'Test message',
	},
};

// Mock webhook data
export const mockWebhookData = {
	message: {
		message_id: 123,
		from: {
			id: 123456789,
			is_bot: false,
			first_name: 'TestUser',
			username: 'testuser',
		},
		chat: {
			id: 123456789,
			type: 'private',
		},
		date: Math.floor(Date.now() / 1000),
		text: 'Hello bot!',
	},
};

// Mock Telegram functions
export const sendTelegramMessage = vi.fn().mockResolvedValue(mockTelegramResponse);
export const sendTelegramError = vi.fn().mockResolvedValue(mockTelegramResponse);
export const sendTypingAction = vi.fn().mockResolvedValue(mockTelegramResponse);
