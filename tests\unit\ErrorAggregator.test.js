import { describe, it, expect, beforeEach } from 'vitest';
import { ErrorAggregator } from '../../src/chat/gemini-ai/core/utils/ErrorAggregator.js';

describe('ErrorAggregator', () => {
	let errorAggregator;

	beforeEach(() => {
		errorAggregator = new ErrorAggregator();
	});

	describe('constructor', () => {
		it('should initialize with empty errors map and null lastError', () => {
			expect(errorAggregator.errors.size).toBe(0);
			expect(errorAggregator.lastError).toBeNull();
		});
	});

	describe('addError', () => {
		it('should add error with context', () => {
			const error = new Error('Test error message');
			const context = 'gemini/model-1';
			
			errorAggregator.addError(context, error);
			
			expect(errorAggregator.errors.get(context)).toBe('Test error message');
			expect(errorAggregator.lastError).toBe(error);
		});

		it('should update lastError with each new error', () => {
			const error1 = new Error('First error');
			const error2 = new Error('Second error');
			
			errorAggregator.addError('context1', error1);
			expect(errorAggregator.lastError).toBe(error1);
			
			errorAggregator.addError('context2', error2);
			expect(errorAggregator.lastError).toBe(error2);
		});

		it('should handle multiple errors for different contexts', () => {
			const error1 = new Error('Gemini error');
			const error2 = new Error('Groq error');
			
			errorAggregator.addError('gemini/model-1', error1);
			errorAggregator.addError('groq/model-2', error2);
			
			expect(errorAggregator.errors.size).toBe(2);
			expect(errorAggregator.errors.get('gemini/model-1')).toBe('Gemini error');
			expect(errorAggregator.errors.get('groq/model-2')).toBe('Groq error');
		});

		it('should overwrite error for same context', () => {
			const error1 = new Error('First error');
			const error2 = new Error('Second error');
			const context = 'same-context';
			
			errorAggregator.addError(context, error1);
			errorAggregator.addError(context, error2);
			
			expect(errorAggregator.errors.size).toBe(1);
			expect(errorAggregator.errors.get(context)).toBe('Second error');
		});
	});

	describe('createErrorMessage', () => {
		it('should create formatted error message with single error', () => {
			const error = new Error('API timeout');
			errorAggregator.addError('gemini/model-1', error);
			
			const result = errorAggregator.createErrorMessage('Text generation');
			
			expect(result).toBe('Text generation failed. Details: gemini/model-1: API timeout');
		});

		it('should create formatted error message with multiple errors', () => {
			const error1 = new Error('Rate limit exceeded');
			const error2 = new Error('Invalid API key');
			
			errorAggregator.addError('gemini/model-1', error1);
			errorAggregator.addError('openai/gpt-4', error2);
			
			const result = errorAggregator.createErrorMessage('AI processing');
			
			expect(result).toContain('AI processing failed. Details:');
			expect(result).toContain('gemini/model-1: Rate limit exceeded');
			expect(result).toContain('openai/gpt-4: Invalid API key');
		});

		it('should handle empty errors gracefully', () => {
			const result = errorAggregator.createErrorMessage('Empty operation');
			
			expect(result).toBe('Empty operation failed. Details: ');
		});

		it('should preserve error order in message', () => {
			errorAggregator.addError('first', new Error('First error'));
			errorAggregator.addError('second', new Error('Second error'));
			errorAggregator.addError('third', new Error('Third error'));
			
			const result = errorAggregator.createErrorMessage('Multi-error');
			
			// The order should be maintained as insertion order
			const firstIndex = result.indexOf('first: First error');
			const secondIndex = result.indexOf('second: Second error');
			const thirdIndex = result.indexOf('third: Third error');
			
			expect(firstIndex).toBeLessThan(secondIndex);
			expect(secondIndex).toBeLessThan(thirdIndex);
		});
	});

	describe('getLastError', () => {
		it('should return null when no errors added', () => {
			expect(errorAggregator.getLastError()).toBeNull();
		});

		it('should return the last error added', () => {
			const error1 = new Error('First error');
			const error2 = new Error('Second error');
			
			errorAggregator.addError('context1', error1);
			errorAggregator.addError('context2', error2);
			
			expect(errorAggregator.getLastError()).toBe(error2);
		});
	});

	describe('hasErrors', () => {
		it('should return false when no errors added', () => {
			expect(errorAggregator.hasErrors()).toBe(false);
		});

		it('should return true when errors exist', () => {
			const error = new Error('Test error');
			errorAggregator.addError('context', error);
			
			expect(errorAggregator.hasErrors()).toBe(true);
		});

		it('should return true even after multiple errors for same context', () => {
			const error1 = new Error('First error');
			const error2 = new Error('Second error');
			const context = 'same-context';
			
			errorAggregator.addError(context, error1);
			errorAggregator.addError(context, error2);
			
			expect(errorAggregator.hasErrors()).toBe(true);
			expect(errorAggregator.errors.size).toBe(1); // Only one context
		});
	});
});
