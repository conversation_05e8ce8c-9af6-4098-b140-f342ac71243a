/**
 * Parses the incoming request body as JSON.
 * @param {Request} request - The incoming request object.
 * @returns {Promise<object>} The parsed JSON data.
 */
import { sendTelegramError } from '../telegram.js';

export async function parseWebhookData(request) {
	return request.json();
}

/**
 * Basic validation to check if the webhook data contains a message or callback_query.
 * @param {object} webhookData - The parsed webhook data.
 * @returns {boolean} True if the data is considered valid for processing, false otherwise.
 */
export function isValidWebhookData(webhookData) {
	return webhookData && (webhookData.message || webhookData.callback_query);
}

/**
 * Extracts relevant message details from the webhook data.
 * @param {object} webhookData - The parsed webhook data.
 * @returns {{chatId: number | undefined, text: string, photo: Array<object>, document: object, messageDataForLogging: object | undefined}} Extracted details.
 */
export function extractMessageDetails(env, webhookData) {
	const message = webhookData.message || (webhookData.callback_query && webhookData.callback_query.message);
	const chatId = message?.chat?.id;
	let text = message?.text || '';
	const photo = message?.photo || [];
	const document = message?.document || {};

	// Use caption if media is present
	if (photo.length > 0 || document.file_id) {
		text = message?.caption || '';
	}

	const botUsername = env.TELEGRAM_BOT_USERNAME;
	const mentionRegex = new RegExp(`@${botUsername}\\b`, 'gi');
	text = text.replace(mentionRegex, '').trim();

	const messageDataForLogging = chatId
		? {
				text,
				photo: photo.length > 0 ? photo[photo.length - 1] : {},
				document: document,
				media_group_id: message?.media_group_id || '',
				from: webhookData.message?.from || webhookData.callback_query?.from || {},
				date: webhookData.message?.date || Date.now() / 1000,
		  }
		: undefined;

	return { chatId, text, photo, document, messageDataForLogging };
}

/**
 * Handles initial errors occurring before background processing is scheduled.
 * Attempts to send a Telegram error notification.
 * @param {object} c - The Hono context object.
 * @param {Error} error - The error that occurred.
 */
export async function handleInitialError(c, error) {
	console.error('Error in main Telegram webhook handler (before background processing):', error);

	// Try to send error notification via Telegram if available
	if (c.env && typeof c.env.TELEGRAM_BOT_TOKEN !== 'undefined') {
		const context = {
			path: '/hrmny',
			method: 'POST',
		};

		try {
			// Use waitUntil to ensure the notification is sent even if the main handler finishes
			c.executionCtx.waitUntil(sendTelegramError(c.env, error, context));
		} catch (notificationError) {
			console.error('Failed to send error notification:', notificationError);
		}
	}
}
