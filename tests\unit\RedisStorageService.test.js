import { describe, it, expect, vi, beforeEach } from 'vitest';
import { RedisStorageService } from '../../src/chat/memory/services/RedisStorageService.js';
import { mockEnv, mockRedisClient } from '../setup.js';

// Mock the getRedisClient function
vi.mock('../../src/redis.js', () => ({
	getRedisClient: vi.fn(() => mockRedisClient),
}));

describe('RedisStorageService', () => {
	let redisService;
	let consoleSpy;

	beforeEach(() => {
		vi.clearAllMocks();
		redisService = new RedisStorageService(mockEnv);
		consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
	});

	afterEach(() => {
		consoleSpy.mockRestore();
	});

	describe('constructor', () => {
		it('should initialize with environment and redis client', () => {
			expect(redisService.env).toBe(mockEnv);
			expect(redisService.redis).toBe(mockRedisClient);
		});
	});

	describe('get', () => {
		it('should return value from Redis', async () => {
			const key = 'test-key';
			const value = 'test-value';
			mockRedisClient.get.mockResolvedValue(value);

			const result = await redisService.get(key);

			expect(result).toBe(value);
			expect(mockRedisClient.get).toHaveBeenCalledWith(key);
		});

		it('should return null on Redis error', async () => {
			const key = 'test-key';
			const error = new Error('Redis connection failed');
			mockRedisClient.get.mockRejectedValue(error);

			const result = await redisService.get(key);

			expect(result).toBeNull();
			expect(consoleSpy).toHaveBeenCalledWith(
				'[RedisStorageService] Error during GET operation on key test-key:',
				error
			);
		});

		it('should handle null response from Redis', async () => {
			const key = 'non-existent-key';
			mockRedisClient.get.mockResolvedValue(null);

			const result = await redisService.get(key);

			expect(result).toBeNull();
		});
	});

	describe('set', () => {
		it('should set value in Redis', async () => {
			const key = 'test-key';
			const value = 'test-value';
			mockRedisClient.set.mockResolvedValue('OK');

			await redisService.set(key, value);

			expect(mockRedisClient.set).toHaveBeenCalledWith(key, value, {});
		});

		it('should set value with options', async () => {
			const key = 'test-key';
			const value = 'test-value';
			const options = { ex: 3600 };
			mockRedisClient.set.mockResolvedValue('OK');

			await redisService.set(key, value, options);

			expect(mockRedisClient.set).toHaveBeenCalledWith(key, value, options);
		});

		it('should handle Redis error during set', async () => {
			const key = 'test-key';
			const value = 'test-value';
			const error = new Error('Redis write failed');
			mockRedisClient.set.mockRejectedValue(error);

			await redisService.set(key, value);

			expect(consoleSpy).toHaveBeenCalledWith(
				'[RedisStorageService] Error during SET operation on key test-key:',
				error
			);
		});
	});

	describe('del', () => {
		it('should delete key from Redis', async () => {
			const key = 'test-key';
			mockRedisClient.del.mockResolvedValue(1);

			const result = await redisService.del(key);

			expect(result).toBe(1);
			expect(mockRedisClient.del).toHaveBeenCalledWith(key);
		});

		it('should return 0 when key does not exist', async () => {
			const key = 'non-existent-key';
			mockRedisClient.del.mockResolvedValue(0);

			const result = await redisService.del(key);

			expect(result).toBe(0);
		});

		it('should return 0 on Redis error', async () => {
			const key = 'test-key';
			const error = new Error('Redis delete failed');
			mockRedisClient.del.mockRejectedValue(error);

			const result = await redisService.del(key);

			expect(result).toBe(0);
			expect(consoleSpy).toHaveBeenCalledWith(
				'[RedisStorageService] Error during DEL operation on key test-key:',
				error
			);
		});
	});

	describe('exists', () => {
		it('should return 1 when key exists', async () => {
			const key = 'existing-key';
			mockRedisClient.exists.mockResolvedValue(1);

			const result = await redisService.exists(key);

			expect(result).toBe(1);
			expect(mockRedisClient.exists).toHaveBeenCalledWith(key);
		});

		it('should return 0 when key does not exist', async () => {
			const key = 'non-existent-key';
			mockRedisClient.exists.mockResolvedValue(0);

			const result = await redisService.exists(key);

			expect(result).toBe(0);
		});

		it('should return 0 on Redis error', async () => {
			const key = 'test-key';
			const error = new Error('Redis exists check failed');
			mockRedisClient.exists.mockRejectedValue(error);

			const result = await redisService.exists(key);

			expect(result).toBe(0);
			expect(consoleSpy).toHaveBeenCalledWith(
				'[RedisStorageService] Error during EXISTS operation on key test-key:',
				error
			);
		});
	});

	describe('_handleError', () => {
		it('should log error with operation and key details', () => {
			const operation = 'TEST_OP';
			const key = 'test-key';
			const error = new Error('Test error');

			redisService._handleError(operation, key, error);

			expect(consoleSpy).toHaveBeenCalledWith(
				'[RedisStorageService] Error during TEST_OP operation on key test-key:',
				error
			);
		});
	});
});
