import { getPreviousMessages } from '../redis.js';
import { getHistory } from './search/semanticSearch.js';
import { formatRedisHistory, handleReplyContext } from './history/historyManager.js';
import { formatCurrentMessage } from './utils/formatUtils.js';
import { prepareContext as prepareFactContext } from './facts/factManager.js';
import { handle as handleAIResponse } from './response/aiResponseHandler.js';

/**
 * @typedef {Object} MessageData
 * @property {string} [text] - The message text
 * @property {number} [timestamp] - Unix timestamp of the message
 * @property {string} [role] - Role of the message sender ('user' or 'assistant')
 * @property {Object} [from] - Sender information
 * @property {boolean} [from.is_bot] - Whether the sender is a bot
 * @property {string} [from.first_name] - Sender's first name
 * @property {string} [from.username] - Sender's username
 * @property {Object} [replyToMessage] - The message being replied to
 * @property {string} [replyToMessage.text] - Text of the replied message
 * @property {string} [replyToMessage.caption] - Caption of the replied media
 */

/**
 * Prepares the chat history string for the AI prompt.
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID
 * @param {string} botUsername - The bot's username
 * @param {string} currentMessageText - The current message text
 * @param {string} senderFirstName - First name of the message sender
 * @param {number} [messageDate] - Timestamp of the message
 * @param {Object} messageData - Full message data object
 * @returns {Promise<{chatContent: string, previousMessages: Array, chatHistoryFormatted: string}>}
 */
export async function prepareChatContext(env, chatId, botUsername, currentMessageText, senderFirstName, messageDate, messageData) {
	try {
		// Get previous messages from Redis
		let previousMessages = await getPreviousMessages(env, chatId, botUsername);

		// Avoid duplicating the current message if Redis already has it
		if (messageDate && previousMessages.length > 0 && previousMessages[previousMessages.length - 1].date === messageDate) {
			previousMessages.pop();
		}

		// Get semantic search results (pass Redis messages to filter out newer vector messages)
		const semanticHistory = await getHistory(env, chatId, currentMessageText, previousMessages);

		let redisHistory = formatRedisHistory(previousMessages, env);

		// Add reply context if present
		const replyContext = handleReplyContext(messageData);
		if (replyContext) {
			redisHistory = redisHistory ? `${redisHistory}\n${replyContext}` : replyContext;
		}

		// Format the current user message
		const chatContent = formatCurrentMessage(currentMessageText, senderFirstName);

		console.log("Prepared Chat Content (User's current message):", chatContent);

		return {
			chatContent,
			previousMessages,
			redisHistory,
			semanticHistory,
		};
	} catch (error) {
		console.error('Error in prepareChatContext:', error);
		// Return minimal context in case of error
		return {
			chatContent: formatCurrentMessage(currentMessageText, senderFirstName),
			previousMessages: [],
			redisHistory: '',
			semanticHistory: '',
		};
	}
}

// Re-export functions for backward compatibility
export { prepareFactContext, handleAIResponse };
