import { describe, it, expect, vi, beforeEach } from 'vitest';
import { parseWebhookData, isValidWebhookData, extractMessageDetails } from '../../src/utils/telegramWebhookHelpers.js';
import { mockEnv } from '../setup.js';

describe('telegramWebhookHelpers', () => {
	describe('parseWebhookData', () => {
		it('should parse JSON from request', async () => {
			const mockRequest = {
				json: vi.fn().mockResolvedValue({ message: { text: 'test' } }),
			};

			const result = await parseWebhookData(mockRequest);

			expect(result).toEqual({ message: { text: 'test' } });
			expect(mockRequest.json).toHaveBeenCalledOnce();
		});

		it('should handle JSON parsing errors', async () => {
			const mockRequest = {
				json: vi.fn().mockRejectedValue(new Error('Invalid JSON')),
			};

			await expect(parseWebhookData(mockRequest)).rejects.toThrow('Invalid JSON');
		});
	});

	describe('isValidWebhookData', () => {
		it('should return true for valid message data', () => {
			const webhookData = {
				message: {
					text: 'Hello',
					from: { id: 123 },
				},
			};

			const result = isValidWebhookData(webhookData);
			expect(result).toBe(true);
		});

		it('should return true for valid callback_query data', () => {
			const webhookData = {
				callback_query: {
					data: 'button_clicked',
					from: { id: 123 },
				},
			};

			const result = isValidWebhookData(webhookData);
			expect(result).toBe(true);
		});

		it('should return false for null data', () => {
			const result = isValidWebhookData(null);
			expect(result).toBe(false);
		});

		it('should return false for undefined data', () => {
			const result = isValidWebhookData(undefined);
			expect(result).toBe(false);
		});

		it('should return false for empty object', () => {
			const result = isValidWebhookData({});
			expect(result).toBe(false);
		});

		it('should return false for data without message or callback_query', () => {
			const webhookData = {
				update_id: 123,
				some_other_field: 'value',
			};

			const result = isValidWebhookData(webhookData);
			expect(result).toBe(false);
		});
	});

	describe('extractMessageDetails', () => {
		let env;

		beforeEach(() => {
			env = { ...mockEnv };
		});

		it('should extract basic message details', () => {
			const webhookData = {
				message: {
					chat: { id: 123456789 },
					text: 'Hello @test_bot how are you?',
					from: { id: 987654321, first_name: 'John' },
					date: 1640995200,
					photo: [],
					document: null,
				},
			};

			const result = extractMessageDetails(env, webhookData);

			expect(result.chatId).toBe(123456789);
			expect(result.text).toBe('Hello  how are you?'); // Bot mention removed
			expect(result.photo).toEqual([]);
			expect(result.document).toEqual({}); // Should be empty object, not null
			expect(result.messageDataForLogging).toEqual({
				text: 'Hello  how are you?',
				photo: {},
				document: {},
				media_group_id: '',
				from: { id: 987654321, first_name: 'John' },
				date: 1640995200,
			});
		});

		it('should handle callback_query data', () => {
			const webhookData = {
				callback_query: {
					message: {
						chat: { id: 123456789 },
						text: 'Button message',
					},
					from: { id: 987654321, first_name: 'Jane' },
					data: 'button_clicked',
				},
			};

			const result = extractMessageDetails(env, webhookData);

			expect(result.chatId).toBe(123456789);
			expect(result.text).toBe('Button message'); // Text from callback_query message
			expect(result.messageDataForLogging).toEqual({
				text: 'Button message',
				photo: {},
				document: {},
				media_group_id: '',
				from: { id: 987654321, first_name: 'Jane' },
				date: expect.any(Number),
			});
		});

		it('should handle photo messages', () => {
			const webhookData = {
				message: {
					chat: { id: 123456789 },
					text: 'Check this photo @test_bot',
					photo: [
						{ file_id: 'photo1', width: 100, height: 100 },
						{ file_id: 'photo2', width: 200, height: 200 },
					],
					from: { id: 987654321 },
					date: 1640995200,
				},
			};

			const result = extractMessageDetails(env, webhookData);

			expect(result.photo).toHaveLength(2);
			expect(result.messageDataForLogging.photo).toEqual({
				file_id: 'photo2',
				width: 200,
				height: 200,
			}); // Should use the largest photo
		});

		it('should handle document messages', () => {
			const webhookData = {
				message: {
					chat: { id: 123456789 },
					text: 'Here is a document',
					document: {
						file_id: 'doc123',
						file_name: 'test.pdf',
						mime_type: 'application/pdf',
					},
					from: { id: 987654321 },
					date: 1640995200,
					photo: [],
				},
			};

			const result = extractMessageDetails(env, webhookData);

			expect(result.document).toEqual({
				file_id: 'doc123',
				file_name: 'test.pdf',
				mime_type: 'application/pdf',
			});
		});

		it('should handle media group messages', () => {
			const webhookData = {
				message: {
					chat: { id: 123456789 },
					text: 'Media group',
					media_group_id: 'group123',
					photo: [{ file_id: 'photo1' }],
					from: { id: 987654321 },
					date: 1640995200,
				},
			};

			const result = extractMessageDetails(env, webhookData);

			expect(result.messageDataForLogging.media_group_id).toBe('group123');
		});

		it('should return undefined messageDataForLogging when no chatId', () => {
			const webhookData = {
				message: {
					text: 'Hello',
					// No chat field
				},
			};

			const result = extractMessageDetails(env, webhookData);

			expect(result.chatId).toBeUndefined();
			expect(result.messageDataForLogging).toBeUndefined();
		});

		it('should remove bot mentions correctly', () => {
			const webhookData = {
				message: {
					chat: { id: 123456789 },
					text: 'Hello @test_bot, how are you @test_bot?',
					from: { id: 987654321 },
					date: 1640995200,
					photo: [],
				},
			};

			const result = extractMessageDetails(env, webhookData);

			expect(result.text).toBe('Hello , how are you ?');
		});
	});
});
