import { getRedisClient } from './redisClient.js';

export const getMediaGroup = async (env, mediaGroupId) => {
	const redis = getRedisClient(env);
	const mediaGroupKey = `mediaGroup:${mediaGroupId}`;
	try {
		const fileInfoObjects = await redis.lrange(mediaGroupKey, 0, -1);
		// If already parsed, just filter out null/undefined
		return (fileInfoObjects || []).filter((item) => item != null);
	} catch (err) {
		console.error(`Redis lrange error for key ${mediaGroupKey}:`, err);
		return [];
	}
};
