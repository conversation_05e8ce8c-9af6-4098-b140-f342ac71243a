import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock <PERSON> messages
vi.mock('@langchain/core/messages', () => ({
	HumanMessage: vi.fn(),
	SystemMessage: vi.fn(),
}));

// Mock GeminiAIError
vi.mock('../../src/chat/gemini-ai/errors/GeminiErrors.js', () => ({
	GeminiAIError: vi.fn(),
}));

import { MessageConverter } from '../../src/chat/gemini-ai/utils/MessageConverter.js';

describe('MessageConverter', () => {
	beforeEach(async () => {
		vi.clearAllMocks();

		const { HumanMessage, SystemMessage } = await import('@langchain/core/messages');
		const { GeminiAIError } = await import('../../src/chat/gemini-ai/errors/GeminiErrors.js');

		HumanMessage.mockImplementation((content) => ({ type: 'human', ...content }));
		SystemMessage.mockImplementation((content) => ({ type: 'system', ...content }));
		GeminiAIError.mockImplementation((message) => new Error(message));
	});

	describe('convertToLangChainMessages', () => {
		it('should convert simple user message', () => {
			const contents = [
				{
					role: 'user',
					parts: [{ text: 'Hello, how are you?' }],
				},
			];

			const result = MessageConverter.convertToLangChainMessages(contents);

			expect(result).toHaveLength(1);
			const { HumanMessage } = await import('@langchain/core/messages');
		expect(HumanMessage).toHaveBeenCalledWith({
				content: 'Hello, how are you?',
			});
		});

		it('should add system message when systemInstruction provided', () => {
			const contents = [
				{
					role: 'user',
					parts: [{ text: 'Hello' }],
				},
			];
			const systemInstruction = 'You are a helpful assistant';

			const result = MessageConverter.convertToLangChainMessages(contents, systemInstruction);

			expect(result).toHaveLength(2);
			const { HumanMessage, SystemMessage } = await import('@langchain/core/messages');
			expect(SystemMessage).toHaveBeenCalledWith({
				content: 'You are a helpful assistant',
			});
			expect(HumanMessage).toHaveBeenCalledWith({
				content: 'Hello',
			});
		});

		it('should handle multiple messages', () => {
			const contents = [
				{
					role: 'user',
					parts: [{ text: 'First message' }],
				},
				{
					role: 'assistant',
					parts: [{ text: 'Assistant response' }],
				},
				{
					role: 'user',
					parts: [{ text: 'Second message' }],
				},
			];

			const result = MessageConverter.convertToLangChainMessages(contents);

			expect(result).toHaveLength(3);
			const { HumanMessage } = await import('@langchain/core/messages');
			expect(HumanMessage).toHaveBeenCalledTimes(2);
		});

		it('should handle messages with multiple parts', () => {
			const contents = [
				{
					role: 'user',
					parts: [{ text: 'First part. ' }, { text: 'Second part.' }],
				},
			];

			const result = MessageConverter.convertToLangChainMessages(contents);

			const { HumanMessage } = await import('@langchain/core/messages');
			expect(HumanMessage).toHaveBeenCalledWith({
				content: 'First part. Second part.',
			});
		});

		it('should handle empty parts array', () => {
			const contents = [
				{
					role: 'user',
					parts: [],
				},
			];

			const result = MessageConverter.convertToLangChainMessages(contents);

			const { HumanMessage } = await import('@langchain/core/messages');
			expect(HumanMessage).toHaveBeenCalledWith({
				content: '',
			});
		});

		it('should handle parts without text property', () => {
			const contents = [
				{
					role: 'user',
					parts: [
						{ text: 'Valid text' },
						{ image: 'base64data' }, // Part without text
						{ text: 'More text' },
					],
				},
			];

			const result = MessageConverter.convertToLangChainMessages(contents);

			const { HumanMessage } = await import('@langchain/core/messages');
			expect(HumanMessage).toHaveBeenCalledWith({
				content: 'Valid text More text',
			});
		});

		it('should throw error for non-array contents', () => {
			const contents = 'not an array';

			expect(() => {
				MessageConverter.convertToLangChainMessages(contents);
			}).toThrow();

			const { GeminiAIError } = await import('../../src/chat/gemini-ai/errors/GeminiErrors.js');
			expect(GeminiAIError).toHaveBeenCalledWith('Contents must be an array');
		});

		it('should throw error for null contents', () => {
			expect(() => {
				MessageConverter.convertToLangChainMessages(null);
			}).toThrow();

			const { GeminiAIError } = await import('../../src/chat/gemini-ai/errors/GeminiErrors.js');
			expect(GeminiAIError).toHaveBeenCalledWith('Contents must be an array');
		});

		it('should throw error for undefined contents', () => {
			expect(() => {
				MessageConverter.convertToLangChainMessages(undefined);
			}).toThrow();

			const { GeminiAIError } = await import('../../src/chat/gemini-ai/errors/GeminiErrors.js');
			expect(GeminiAIError).toHaveBeenCalledWith('Contents must be an array');
		});

		it('should handle empty contents array', () => {
			const contents = [];

			const result = MessageConverter.convertToLangChainMessages(contents);

			expect(result).toHaveLength(0);
			const { HumanMessage, SystemMessage } = await import('@langchain/core/messages');
			expect(HumanMessage).not.toHaveBeenCalled();
			expect(SystemMessage).not.toHaveBeenCalled();
		});

		it('should handle empty system instruction', () => {
			const contents = [
				{
					role: 'user',
					parts: [{ text: 'Hello' }],
				},
			];

			const result = MessageConverter.convertToLangChainMessages(contents, '');

			expect(result).toHaveLength(1);
			const { HumanMessage, SystemMessage } = await import('@langchain/core/messages');
			expect(SystemMessage).not.toHaveBeenCalled();
			expect(HumanMessage).toHaveBeenCalledWith({
				content: 'Hello',
			});
		});

		it('should handle messages without parts property', () => {
			const contents = [
				{
					role: 'user',
					// No parts property
				},
			];

			const result = MessageConverter.convertToLangChainMessages(contents);

			expect(mockHumanMessage).toHaveBeenCalledWith({
				content: '',
			});
		});
	});
});
