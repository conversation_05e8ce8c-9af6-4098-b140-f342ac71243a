import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { secureHeaders } from 'hono/secure-headers';

// Import custom middleware
import { validationMiddleware, hostnameCheckMiddleware, errorHandlerMiddleware } from './middleware';

// Import application routes
import routes from './routes/index.js';

// Initialize Hono app
const app = new Hono();

// === Global Middleware Registration ===
// NOTE: Order matters!

// 1. <PERSON><PERSON><PERSON> (registered first to wrap everything, but executes last on error)
//    Alternatively, register it last to only catch errors not handled by specific routes/middleware.
//    Registering first ensures it catches errors even during middleware execution.
app.use('*', errorHandlerMiddleware);

// 2. Security Headers
app.use('*', secureHeaders());

// 3. CORS Handling
//    Configure origins as needed, potentially from environment variables
app.use('*', cors(/* { origin: 'YOUR_FRONTEND_URL' } */));

// 4. Hostname Check (apply early)
app.use('*', hostnameCheckMiddleware);

// 6. Request Validation (specific to routes like /lyrics/:id)
//    This could be applied globally or specifically to the lyrics route.
//    Applying globally here, but the middleware itself checks the path.
app.use('*', validationMiddleware);

// === Route Mounting ===
// Mount all defined routes
app.route('/', routes);

// === Default Export for Cloudflare Worker ===
export default app;
