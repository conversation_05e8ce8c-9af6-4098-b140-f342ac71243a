/**
 * Manages user facts and insights
 * @module factManager
 */

import { extractAndStoreFacts, getUserFacts } from '../../redis.js';

/**
 * Prepares the user facts context and triggers fact extraction
 * @param {Object} env - Environment variables
 * @param {string|number} userId - The user ID
 * @param {string} firstName - User's first name
 * @param {string} currentMessageText - Current message text
 * @param {Array<Object>} previousMessages - Previous messages in the conversation
 * @returns {Promise<{factsString: string}>}
 */
export async function prepareContext(env, userId, firstName, currentMessageText, previousMessages) {
	try {
		// Get user facts
		const userFacts = await getUserFacts(env, userId);
		const factsString = userFacts.map(String).join('\n') || '';

		// Extract and store facts in background
		extractAndStoreFacts(env, userId, firstName, currentMessageText, previousMessages).catch((error) =>
			console.error('Error in background fact extraction:', error)
		);

		console.log('Prepared Facts String:', factsString);

		return { factsString };
	} catch (error) {
		console.error('Error in prepareFactContext:', error);
		return { factsString: '' };
	}
}
