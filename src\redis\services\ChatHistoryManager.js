import { getRedisClient } from '../redisClient.js';

export class ChatHistoryManager {
	constructor(env) {
		this.env = env;
		this.redis = getRedisClient(env);
	}

	async deleteChatHistory(chatId) {
		const key = this._buildKey(chatId);

		try {
			const exists = await this.redis.exists(key);
			if (!exists) {
				console.log(`[ChatHistoryManager] No chat history found for chat ${chatId}`);
				return true;
			}

			const result = await this.redis.del(key);
			const success = result > 0;

			console.log(`[ChatHistoryManager] ${success ? 'Successfully deleted' : 'Failed to delete'} chat history for chat ${chatId}`);
			return success || true; // Consider non-existent keys as successful deletion
		} catch (error) {
			console.error(`[ChatHistoryManager] Error deleting chat history for chat ${chatId}:`, error);
			return false;
		}
	}

	_buildKey(chatId) {
		return `${this.env.TELEGRAM_BOT_USERNAME}:chat:${chatId.toString()}:messages`;
	}
}
