/**
 * @fileoverview API key management with rotation and caching for Gemini API.
 */

import { ApiKeyError } from '../errors/GeminiErrors.js';

/**
 * Manages API key rotation and caching for Gemini API
 */
export class ApiKeyManager {
	constructor() {
		this.currentApiKeyIndex = 0;
		this.geminiApiKeysCache = [];
		this.lastEnvKeysString = '';
	}

	/**
	 * Loads Gemini API keys from environment variables
	 * @param {object} env - Environment variables
	 * @returns {string[]} Array of API keys
	 */
	loadGeminiApiKeys(env) {
		// Check if environment variables related to keys have changed
		const currentEnvKeysString = JSON.stringify(
			Object.keys(env)
				.filter((key) => key.startsWith('GEMINI_API_KEY_'))
				.sort()
				.map((key) => env[key])
		);

		if (currentEnvKeysString === this.lastEnvKeysString && this.geminiApiKeysCache.length > 0) {
			return this.geminiApiKeysCache; // Return cached keys if no change
		}

		const keys = [];
		let i = 1;
		while (env[`GEMINI_API_KEY_${i}`]) {
			keys.push(env[`GEMINI_API_KEY_${i}`]);
			i++;
		}

		if (keys.length === 0 && env.GEMINI_API_KEY) {
			console.warn('No numbered Gemini API keys found. Falling back to single GEMINI_API_KEY from environment.');
			keys.push(env.GEMINI_API_KEY);
		}

		this.geminiApiKeysCache = keys;
		this.lastEnvKeysString = currentEnvKeysString;
		this.currentApiKeyIndex = 0; // Reset index when keys are reloaded
		return keys;
	}

	/**
	 * Gets the next API key in rotation
	 * @param {object} env - Environment variables
	 * @returns {string} The selected API key
	 * @throws {ApiKeyError} If no API keys are configured
	 */
	getNextGeminiApiKey(env) {
		const apiKeys = this.loadGeminiApiKeys(env);

		if (apiKeys.length === 0) {
			throw new ApiKeyError(
				'No Gemini API keys configured in environment variables (e.g., GEMINI_API_KEY_1, GEMINI_API_KEY_2, or GEMINI_API_KEY).'
			);
		}

		const selectedKey = apiKeys[this.currentApiKeyIndex];
		this.currentApiKeyIndex = (this.currentApiKeyIndex + 1) % apiKeys.length;

		const displayIndex = this.currentApiKeyIndex === 0 ? apiKeys.length - 1 : this.currentApiKeyIndex - 1;
		console.log(`Using Gemini API key index: ${displayIndex} (${selectedKey.substring(0, 10)}...)`);

		return selectedKey;
	}

	/**
	 * Gets the current API key index for logging purposes
	 * @returns {number} Current API key index
	 */
	getCurrentApiKeyIndex() {
		return this.currentApiKeyIndex === 0 ? this.geminiApiKeysCache.length - 1 : this.currentApiKeyIndex - 1;
	}
}
