import { getMediaGroup } from '../redis.js';
import { getTelegramFile, arrayBufferToBase64 } from './telegramUtils.js';

const ALLOWED_FILE_TYPES = ['application/pdf', 'image/png', 'image/jpeg', 'text/plain'];

/**
 * Processes replied-to attachments (photos and documents).
 */
export async function processRepliedToAttachments(env, messageData) {
	const { replyToPhoto, replyToDocument } = messageData;
	const attachmentParts = [];
	const botToken = env.HRMNY_BOT_TOKEN;

	if (replyToPhoto && replyToPhoto.length > 0) {
		console.log(`Processing single highest resolution photo from replied-to message...`);
		const highestResPhoto = replyToPhoto[replyToPhoto.length - 1]; // Get the last element (the highest resolution)
		const processedPhoto = await fetchAndProcessFile(botToken, highestResPhoto.file_id, 'image/jpeg', ALLOWED_FILE_TYPES);
		if (processedPhoto) {
			attachmentParts.push(processedPhoto);
		}
	}

	if (replyToDocument && replyToDocument.file_id) {
		console.log(`Processing document from replied-to message with file_id: ${replyToDocument.file_id}`);
		const processedDocument = await fetchAndProcessFile(botToken, replyToDocument.file_id, replyToDocument.mime_type, ALLOWED_FILE_TYPES);
		if (processedDocument) {
			attachmentParts.push(processedDocument);
		}
	}

	return attachmentParts;
}

/**
 * Processes media group attachments, with fallback to current message media if needed.
 */
export async function processMediaGroupAttachments(env, messageData) {
	const { mediaGroupId } = messageData;
	if (!mediaGroupId) {
		return [];
	}

	await new Promise((resolve) => setTimeout(resolve, 1500)); // Keep the delay

	try {
		const mediaGroupResult = await getMediaGroup(env, mediaGroupId);
		console.log(`Retrieved ${mediaGroupResult.length} items for media group ${mediaGroupId}`);

		if (Array.isArray(mediaGroupResult)) {
			const mediaGroupItemsToProcess = mediaGroupResult.filter(
				(item) =>
					(item.type === 'photo' && item.file_id) ||
					(item.type === 'document' && item.file_id && item.mime_type && ALLOWED_FILE_TYPES.includes(item.mime_type))
			);

			if (mediaGroupItemsToProcess.length > 0) {
				console.log(`Processing ${mediaGroupItemsToProcess.length} item(s) from media group ${mediaGroupId}`);
				const botToken = env.HRMNY_BOT_TOKEN;
				const processPromises = mediaGroupItemsToProcess.map(async (item) => {
					const mimeType = item.type === 'photo' ? 'image/jpeg' : item.mime_type;
					return await fetchAndProcessFile(botToken, item.file_id, mimeType, ALLOWED_FILE_TYPES);
				});
				const processedItems = await Promise.all(processPromises);
				return processedItems.filter(Boolean); // Filter out any null values
			} else {
				console.warn(`No suitable items found in media group result for ${mediaGroupId}.`);
			}
		} else {
			console.warn(`getMediaGroup did not return an array for media group ${mediaGroupId}. Result:`, mediaGroupResult);
		}
	} catch (e) {
		console.error(`Error getting media group ${mediaGroupId}:`, e);
	}

	// Fallback to processing current message media
	console.log('Falling back to processing current message media');
	return processCurrentAttachments(env, messageData);
}

/**
 * Processes attachments from the current message.
 */
export async function processCurrentAttachments(env, messageData) {
	const { photo, document } = messageData; // Use original names from messageData
	const attachmentParts = [];
	const botToken = env.HRMNY_BOT_TOKEN;

	if (photo.length > 0) {
		console.log('Processing single photo from current message data.');
		const processedPhoto = await fetchAndProcessFile(botToken, photo[photo.length - 1].file_id, 'image/jpeg', ALLOWED_FILE_TYPES);
		if (processedPhoto) {
			attachmentParts.push(processedPhoto);
		}
	} else if (document.file_id) {
		console.log(`Processing single document from current message data.`);
		const processedDocument = await fetchAndProcessFile(botToken, document.file_id, document.mime_type, ALLOWED_FILE_TYPES);
		if (processedDocument) {
			attachmentParts.push(processedDocument);
		}
	}

	return attachmentParts;
}

/**
 * Downloads a file from Telegram and converts it to base64.
 */
export async function downloadAndEncodeFile(botToken, fileId) {
	const fileUrl = await getTelegramFile(botToken, fileId);
	const response = await fetch(fileUrl);
	if (!response.ok) {
		throw new Error(`Failed to fetch file ${fileId}: ${response.statusText}`);
	}
	const fileArrayBuffer = await response.arrayBuffer();
	return arrayBufferToBase64(fileArrayBuffer);
}

/**
 * Helper function to fetch, process, and format a single file attachment.
 */
export async function fetchAndProcessFile(botToken, fileId, mimeType, allowedFileTypes) {
	if (!fileId) {
		return null;
	}
	if (!allowedFileTypes.includes(mimeType)) {
		console.log(`Skipping unsupported file type: ${mimeType}`);
		return null;
	}
	try {
		const base64FileData = await downloadAndEncodeFile(botToken, fileId);
		console.log(`Successfully processed file with file_id: ${fileId}`);
		return { inlineData: { mimeType: mimeType, data: base64FileData } };
	} catch (error) {
		console.error(`Error processing file with file_id ${fileId}:`, error);
		return null;
	}
}

/**
 * Processes attachments (photos and documents) from the message.
 * It collects attachments from replied-to media, media groups, and the current message.
 */
export async function processAttachments(env, messageData) {
	let attachmentParts = [];

	// Process replied-to attachments
	attachmentParts = attachmentParts.concat(await processRepliedToAttachments(env, messageData));

	// Process media group attachments if applicable
	if (messageData.mediaGroupId && attachmentParts.length === 0) {
		attachmentParts = attachmentParts.concat(await processMediaGroupAttachments(env, messageData));
	} else {
		attachmentParts = attachmentParts.concat(await processCurrentAttachments(env, messageData));
	}

	return attachmentParts;
}
