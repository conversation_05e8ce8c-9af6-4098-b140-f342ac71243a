import { describe, it, expect, vi, beforeEach } from 'vitest';
import { hostnameCheckMiddleware } from '../../src/middleware/hostnameCheck.js';
import { mockEnv } from '../setup.js';

describe('hostnameCheckMiddleware', () => {
	let mockContext;
	let mockNext;
	let consoleSpy;
	let consoleWarnSpy;

	beforeEach(() => {
		vi.clearAllMocks();
		mockNext = vi.fn();
		consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
		consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
		
		mockContext = {
			req: {
				header: vi.fn(),
			},
			env: { ...mockEnv },
			json: vi.fn(),
		};
	});

	afterEach(() => {
		consoleSpy.mockRestore();
		consoleWarnSpy.mockRestore();
	});

	describe('hostname matching', () => {
		it('should proceed when hostnames match', async () => {
			mockContext.req.header.mockReturnValue('test.workers.dev');
			mockContext.env.HOSTNAME = 'test.workers.dev';
			
			await hostnameCheckMiddleware(mockContext, mockNext);
			
			expect(mockNext).toHaveBeenCalledOnce();
			expect(mockContext.json).not.toHaveBeenCalled();
			expect(consoleSpy).toHaveBeenCalledWith(
				'Request Host: test.workers.dev, Expected Host: test.workers.dev'
			);
		});

		it('should reject when hostnames do not match in production', async () => {
			mockContext.req.header.mockReturnValue('malicious.example.com');
			mockContext.env.HOSTNAME = 'test.workers.dev';
			mockContext.env.DEV_MODE = 'false';
			
			await hostnameCheckMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				{
					status: 403,
					error: true,
					details: 'Forbidden: Hostname mismatch.',
				},
				403
			);
			expect(consoleWarnSpy).toHaveBeenCalledWith(
				'Hostname mismatch: Request host "malicious.example.com" does not match expected "test.workers.dev".'
			);
		});

		it('should bypass check in development mode when hostnames do not match', async () => {
			mockContext.req.header.mockReturnValue('localhost:8787');
			mockContext.env.HOSTNAME = 'test.workers.dev';
			mockContext.env.DEV_MODE = 'true';
			
			await hostnameCheckMiddleware(mockContext, mockNext);
			
			expect(mockNext).toHaveBeenCalledOnce();
			expect(mockContext.json).not.toHaveBeenCalled();
			expect(consoleSpy).toHaveBeenCalledWith(
				'DEV_MODE enabled, bypassing hostname check.'
			);
		});
	});

	describe('edge cases', () => {
		it('should handle missing request host header', async () => {
			mockContext.req.header.mockReturnValue(undefined);
			mockContext.env.HOSTNAME = 'test.workers.dev';
			mockContext.env.DEV_MODE = 'false';
			
			await hostnameCheckMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				{
					status: 403,
					error: true,
					details: 'Forbidden: Hostname mismatch.',
				},
				403
			);
			expect(consoleSpy).toHaveBeenCalledWith(
				'Request Host: undefined, Expected Host: test.workers.dev'
			);
		});

		it('should handle missing expected hostname', async () => {
			mockContext.req.header.mockReturnValue('test.workers.dev');
			mockContext.env.HOSTNAME = undefined;
			mockContext.env.DEV_MODE = 'false';
			
			await hostnameCheckMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				{
					status: 403,
					error: true,
					details: 'Forbidden: Hostname mismatch.',
				},
				403
			);
		});

		it('should handle null request host', async () => {
			mockContext.req.header.mockReturnValue(null);
			mockContext.env.HOSTNAME = 'test.workers.dev';
			mockContext.env.DEV_MODE = 'false';
			
			await hostnameCheckMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				{
					status: 403,
					error: true,
					details: 'Forbidden: Hostname mismatch.',
				},
				403
			);
		});

		it('should handle empty string hostnames', async () => {
			mockContext.req.header.mockReturnValue('');
			mockContext.env.HOSTNAME = '';
			
			await hostnameCheckMiddleware(mockContext, mockNext);
			
			expect(mockNext).toHaveBeenCalledOnce();
			expect(mockContext.json).not.toHaveBeenCalled();
		});

		it('should be case sensitive for hostname comparison', async () => {
			mockContext.req.header.mockReturnValue('TEST.WORKERS.DEV');
			mockContext.env.HOSTNAME = 'test.workers.dev';
			mockContext.env.DEV_MODE = 'false';
			
			await hostnameCheckMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				{
					status: 403,
					error: true,
					details: 'Forbidden: Hostname mismatch.',
				},
				403
			);
		});
	});

	describe('development mode variations', () => {
		it('should bypass check when DEV_MODE is exactly "true"', async () => {
			mockContext.req.header.mockReturnValue('localhost');
			mockContext.env.HOSTNAME = 'production.com';
			mockContext.env.DEV_MODE = 'true';
			
			await hostnameCheckMiddleware(mockContext, mockNext);
			
			expect(mockNext).toHaveBeenCalledOnce();
		});

		it('should not bypass check when DEV_MODE is "TRUE" (case sensitive)', async () => {
			mockContext.req.header.mockReturnValue('localhost');
			mockContext.env.HOSTNAME = 'production.com';
			mockContext.env.DEV_MODE = 'TRUE';
			
			await hostnameCheckMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				expect.objectContaining({
					status: 403,
					error: true,
				}),
				403
			);
		});

		it('should not bypass check when DEV_MODE is undefined', async () => {
			mockContext.req.header.mockReturnValue('localhost');
			mockContext.env.HOSTNAME = 'production.com';
			delete mockContext.env.DEV_MODE;
			
			await hostnameCheckMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				expect.objectContaining({
					status: 403,
					error: true,
				}),
				403
			);
		});
	});
});
