import { ModelFactory } from '../../factories/ModelFactory.js';
import { MessageConverter } from '../../utils/MessageConverter.js';
import { GROQ_BASE_URL, CEREBRAS_BASE_URL, OPENAI_BASE_URL, GROQ_MODELS, CEREBRAS_MODELS, OPENAI_MODELS } from '../../config/constants.js';

/**
 * Fast provider management class
 */
export class FastProvider {
	constructor() {
		// Cache provider configurations to avoid recreating them
		this._providerConfigCache = null;
	}

	/**
	 * Gets provider configurations with caching
	 * @param {object} env - Environment variables
	 * @returns {object} Provider configurations
	 */
	_getProviderConfigurations(env) {
		if (!this._providerConfigCache) {
			this._providerConfigCache = {
				groq: { baseUrl: GROQ_BASE_URL, models: GROQ_MODELS, apiKey: env.GROQ_API_KEY },
				cerebras: { baseUrl: CEREBRAS_BASE_URL, models: CEREBRAS_MODELS, apiKey: env.CEREBRAS_API_KEY },
				openai: { baseUrl: OPENAI_BASE_URL, models: OPENAI_MODELS, apiKey: env.OPENAI_API_KEY },
			};
		}
		// Update API keys in case they changed
		this._providerConfigCache.groq.apiKey = env.GROQ_API_KEY;
		this._providerConfigCache.cerebras.apiKey = env.CEREBRAS_API_KEY;
		this._providerConfigCache.openai.apiKey = env.OPENAI_API_KEY;

		return this._providerConfigCache;
	}

	/**
	 * Attempts completion with fast providers
	 * @param {object} env - Environment variables
	 * @param {object} config - AI configuration object
	 * @param {Array} contents - Array of message contents
	 * @param {ErrorAggregator} errorAggregator - Error aggregator instance
	 * @returns {Promise<object|null>} Response object or null if all attempts fail
	 */
	async attemptCompletion(env, config, contents, errorAggregator) {
		const providers = this._getProviderConfigurations(env);
		const preferredProvider = config.inferenceProvider || 'cerebras';

		console.log(`Preferred inference provider: ${preferredProvider}`);

		const providerOrder = this._getProviderPriorityOrder(preferredProvider, providers);
		console.log(`Provider retry order: ${providerOrder.join(' -> ')}`);

		for (const providerName of providerOrder) {
			const provider = providers[providerName];

			if (!this._isProviderConfigured(provider)) {
				const errorMsg = `Missing configuration for provider: ${providerName}`;
				console.warn(errorMsg);
				errorAggregator.addError(providerName, new Error(errorMsg));
				continue;
			}

			console.log(`Trying provider: ${providerName}`);

			const providerConfig = {
				...config,
				baseUrl: provider.baseUrl,
				apiKey: provider.apiKey,
			};

			const modelNames = provider.models
				.split(',')
				.map((m) => m.trim())
				.filter(Boolean);

			for (const modelName of modelNames) {
				try {
					console.log(`Attempting ${providerName} model: ${modelName}`);
					const result = await this._attemptModelCompletion(modelName, providerConfig, contents);
					console.log(`✅ Success with ${providerName}/${modelName}`);
					return result;
				} catch (error) {
					const context = `${providerName}/${modelName}`;
					console.error(`${context} failed:`, error);
					errorAggregator.addError(context, error);
				}
			}
		}
		return null;
	}

	/**
	 * Checks if a provider is properly configured
	 * @param {object} provider - Provider configuration
	 * @returns {boolean} True if configured
	 * @private
	 */
	_isProviderConfigured(provider) {
		return provider && provider.baseUrl && provider.apiKey;
	}

	/**
	 * Gets the provider priority order
	 * @param {string} preferredProvider - The preferred provider to try first
	 * @param {object} availableProviders - Object containing all available providers
	 * @returns {string[]} Array of provider names in priority order
	 * @private
	 */
	_getProviderPriorityOrder(preferredProvider, availableProviders) {
		const allProviders = Object.keys(availableProviders);
		const priorityOrder = [];

		if (preferredProvider && allProviders.includes(preferredProvider)) {
			priorityOrder.push(preferredProvider);
		}

		allProviders.forEach((provider) => {
			if (provider !== preferredProvider) {
				priorityOrder.push(provider);
			}
		});

		return priorityOrder;
	}

	/**
	 * Attempts chat completion with a specific fast model
	 * @param {string} modelName - Name of the model to use
	 * @param {object} config - AI configuration object
	 * @param {Array} contents - Array of message contents
	 * @returns {Promise<object>} Response object with text
	 * @private
	 */
	async _attemptModelCompletion(modelName, config, contents) {
		console.log(`Attempting chat completion with model: ${modelName}`);

		const model = ModelFactory.createFastModel(modelName, config);
		const messages = MessageConverter.convertToLangChainMessages(contents, config.systemInstruction);

		const response = await model.invoke(messages);
		console.log(`Successfully completed chat with model: ${modelName}`);

		// Remove any <think> block if present
		const cleanedContent = response.content.replace(/<think>[\s\S]*?<\/think>/gi, '');

		return {
			text: cleanedContent,
		};
	}
}
