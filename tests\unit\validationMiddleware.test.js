import { describe, it, expect, vi, beforeEach } from 'vitest';
import { validationMiddleware } from '../../src/middleware/validation.js';

describe('validationMiddleware', () => {
	let mockContext;
	let mockNext;
	let consoleSpy;

	beforeEach(() => {
		mockNext = vi.fn();
		consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
		
		mockContext = {
			req: {
				method: 'GET',
				path: '/test',
				header: vi.fn(),
				param: vi.fn(),
			},
			json: vi.fn(),
		};
	});

	afterEach(() => {
		consoleSpy.mockRestore();
	});

	describe('Content-Type validation', () => {
		it('should pass for GET requests without Content-Type check', async () => {
			mockContext.req.method = 'GET';
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).toHaveBeenCalledOnce();
			expect(mockContext.json).not.toHaveBeenCalled();
		});

		it('should pass for POST requests with valid Content-Type', async () => {
			mockContext.req.method = 'POST';
			mockContext.req.header.mockReturnValue('application/json');
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).toHaveBeenCalledOnce();
			expect(mockContext.json).not.toHaveBeenCalled();
		});

		it('should pass for POST requests with Content-Type containing application/json', async () => {
			mockContext.req.method = 'POST';
			mockContext.req.header.mockReturnValue('application/json; charset=utf-8');
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).toHaveBeenCalledOnce();
		});

		it('should reject POST requests with invalid Content-Type', async () => {
			mockContext.req.method = 'POST';
			mockContext.req.header.mockReturnValue('text/plain');
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				{
					error: true,
					details: 'Invalid request: Content-Type must be application/json.',
					status: 400,
				},
				400
			);
		});

		it('should reject POST requests with missing Content-Type', async () => {
			mockContext.req.method = 'POST';
			mockContext.req.header.mockReturnValue(null);
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				{
					error: true,
					details: 'Invalid request: Content-Type must be application/json.',
					status: 400,
				},
				400
			);
		});

		it('should validate PUT requests', async () => {
			mockContext.req.method = 'PUT';
			mockContext.req.header.mockReturnValue('application/json');
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).toHaveBeenCalledOnce();
		});

		it('should validate PATCH requests', async () => {
			mockContext.req.method = 'PATCH';
			mockContext.req.header.mockReturnValue('text/plain');
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				expect.objectContaining({
					error: true,
					status: 400,
				}),
				400
			);
		});
	});

	describe('ID parameter validation', () => {
		it('should pass for non-lyrics routes', async () => {
			mockContext.req.path = '/health';
			mockContext.req.param.mockReturnValue(undefined);
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).toHaveBeenCalledOnce();
		});

		it('should pass for lyrics routes with valid ID', async () => {
			mockContext.req.path = '/lyrics/abc123XYZ';
			mockContext.req.param.mockReturnValue('abc123XYZ');
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).toHaveBeenCalledOnce();
		});

		it('should reject lyrics routes with empty ID', async () => {
			mockContext.req.path = '/lyrics/';
			mockContext.req.param.mockReturnValue('');
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				{
					error: true,
					details: 'Invalid request: Track ID is missing in the URL path.',
					status: 400,
				},
				400
			);
		});

		it('should reject lyrics routes with invalid ID format', async () => {
			mockContext.req.path = '/lyrics/invalid-id-with-dashes';
			mockContext.req.param.mockReturnValue('invalid-id-with-dashes');
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				{
					error: true,
					details: 'Invalid request: Track ID format is invalid.',
					status: 400,
				},
				400
			);
		});

		it('should reject lyrics routes with special characters in ID', async () => {
			mockContext.req.path = '/lyrics/abc@123';
			mockContext.req.param.mockReturnValue('abc@123');
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(consoleSpy).toHaveBeenCalledWith('Invalid track ID format: abc@123');
		});

		it('should handle lyrics routes without ID parameter', async () => {
			mockContext.req.path = '/lyrics/';
			mockContext.req.param.mockReturnValue(undefined);
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).toHaveBeenCalledOnce(); // Should pass since no ID to validate
		});
	});

	describe('Combined validation', () => {
		it('should validate both Content-Type and ID for POST to lyrics route', async () => {
			mockContext.req.method = 'POST';
			mockContext.req.path = '/lyrics/validID123';
			mockContext.req.header.mockReturnValue('application/json');
			mockContext.req.param.mockReturnValue('validID123');
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).toHaveBeenCalledOnce();
		});

		it('should fail on Content-Type before checking ID', async () => {
			mockContext.req.method = 'POST';
			mockContext.req.path = '/lyrics/validID123';
			mockContext.req.header.mockReturnValue('text/plain');
			mockContext.req.param.mockReturnValue('validID123');
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				expect.objectContaining({
					details: 'Invalid request: Content-Type must be application/json.',
				}),
				400
			);
		});

		it('should check ID validation after Content-Type passes', async () => {
			mockContext.req.method = 'POST';
			mockContext.req.path = '/lyrics/invalid-id';
			mockContext.req.header.mockReturnValue('application/json');
			mockContext.req.param.mockReturnValue('invalid-id');
			
			await validationMiddleware(mockContext, mockNext);
			
			expect(mockNext).not.toHaveBeenCalled();
			expect(mockContext.json).toHaveBeenCalledWith(
				expect.objectContaining({
					details: 'Invalid request: Track ID format is invalid.',
				}),
				400
			);
		});
	});
});
