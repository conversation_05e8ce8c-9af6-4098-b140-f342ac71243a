import { sendTelegramError } from '../telegram.js';

/**
 * Hono middleware for centralized error handling.
 * Catches errors thrown by preceding middleware or route handlers.
 * Logs the error and returns a standardized JSON error response.
 */
export const errorHandlerMiddleware = async (c, next) => {
	try {
		// Attempt to proceed with the request handling chain
		await next();

		// Handle cases where a response might not have been set properly downstream,
		// although Hon<PERSON> usually handles 404s automatically if no route matches.
		// This check might be redundant depending on <PERSON><PERSON>'s behavior.
		if (!c.res.status) {
			console.warn(`No response set for path: ${c.req.path}, returning 404.`);
			// You might want to return a standard 404 JSON response here
			// return c.json({ error: true, details: 'Not Found', status: 404 }, 404);
		}
	} catch (err) {
		// Log the error with details
		console.error('Unhandled error caught in errorHandlerMiddleware:', {
			message: err.message,
			stack: err.stack,
			path: c.req.path,
			method: c.req.method,
			// Optionally log request headers or body (be careful with sensitive data)
		});

		// Send error report to Telegram asynchronously (don't block response)
		sendTelegramError(c.env, err, { path: c.req.path, method: c.req.method });

		// Determine the status code - default to 500
		const statusCode = err.status || err.statusCode || 500;

		// Return a standardized JSON error response
		// Avoid leaking stack traces or sensitive details in production
		const errorResponse = {
			error: true,
			details: err.message || 'An unexpected internal server error occurred.',
			status: statusCode,
		};

		// In development mode, you might want to include more details
		if (c.env.DEV_MODE === 'true' && err.stack) {
			errorResponse.stack = err.stack; // Include stack trace only in dev
		}

		return c.json(errorResponse, statusCode);
	}
};
