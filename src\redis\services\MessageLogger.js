import { getRedisClient } from '../redisClient.js';

export class MessageLogger {
	constructor(env) {
		this.env = env;
		this.redis = getRedisClient(env);
		this.MAX_MESSAGES = 100;
	}

	async logMessage(chatId, messageData) {
		const key = this._buildKey(chatId);

		try {
			const existingMessages = await this._getExistingMessages(key);
			const updatedMessages = this._updateMessages(existingMessages, messageData);

			await this._storeMessages(key, updatedMessages, messageData);
			console.log(`Logged message to Redis for chat ${chatId}`);
		} catch (error) {
			console.error(`Error logging message to Redis for chat ${chatId}: ${error.message}`);
		}
	}

	_buildKey(chatId) {
		return `${this.env.TELEGRAM_BOT_USERNAME}:chat:${chatId.toString()}:messages`;
	}

	async _getExistingMessages(key) {
		const result = await this.redis.pipeline().get(key).exec();
		return result[0] || [];
	}

	_updateMessages(existingMessages, messageData) {
		existingMessages.push(messageData);
		return existingMessages.length > this.MAX_MESSAGES ? existingMessages.slice(-this.MAX_MESSAGES) : existingMessages;
	}

	async _storeMessages(key, messages, messageData) {
		const pipeline = this.redis.pipeline();
		pipeline.set(key, JSON.stringify(messages));

		const fileInfo = this._extractFileInfo(messageData);
		if (fileInfo) {
			this._addMediaGroupToPipeline(pipeline, messageData.media_group_id, fileInfo);
		}

		await pipeline.exec();
	}

	_extractFileInfo(messageData) {
		if (!messageData.media_group_id) return null;

		if (messageData.photo?.file_id) {
			return { type: 'photo', file_id: messageData.photo.file_id };
		}
		if (messageData.document?.file_id) {
			return {
				type: 'document',
				file_id: messageData.document.file_id,
				mime_type: messageData.document.mime_type,
			};
		}
		return null;
	}

	_addMediaGroupToPipeline(pipeline, mediaGroupId, fileInfo) {
		const mediaGroupKey = `mediaGroup:${mediaGroupId}`;
		pipeline.rpush(mediaGroupKey, JSON.stringify(fileInfo));
		pipeline.expire(mediaGroupKey, 3600);
		console.log(`Appended file info ${JSON.stringify(fileInfo)} to media group ${mediaGroupId}`);
	}
}
