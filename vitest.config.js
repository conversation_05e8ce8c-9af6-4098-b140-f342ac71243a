import { defineConfig } from 'vitest/config';

export default defineConfig({
	test: {
		environment: 'happy-dom', // Use happy-dom for DOM simulation
		globals: true, // Enable global test functions like describe, it, expect
		setupFiles: ['./tests/setup.js'], // Setup file for test configuration
		coverage: {
			provider: 'v8',
			reporter: ['text', 'json', 'html'],
			exclude: [
				'node_modules/',
				'tests/',
				'*.config.js',
				'wrangler.toml',
				'dist/',
			],
		},
		// Mock Cloudflare Workers environment
		define: {
			global: 'globalThis',
		},
	},
	resolve: {
		alias: {
			'@': './src',
		},
	},
});
