/**
 * Hono middleware to validate incoming requests, checking Content-Type and path parameters.
 * Specifically designed for routes like /lyrics/:id
 */
export const validationMiddleware = async (c, next) => {
	// 1. Check Content-Type for POST/PUT/PATCH requests (adjust methods as needed)
	// This check is primarily relevant for the POST /lyrics/:id route
	if (['POST', 'PUT', 'PATCH'].includes(c.req.method)) {
		const contentType = c.req.header('Content-Type');
		if (!contentType || !contentType.toLowerCase().includes('application/json')) {
			console.log(`Invalid Content-Type: ${contentType}`);
			return c.json(
				{
					error: true,
					details: 'Invalid request: Content-Type must be application/json.',
					status: 400,
				},
				400
			);
		}
	}

	// 2. Check if 'id' path parameter is present and valid (alphanumeric)
	// This applies to routes that expect an ID, like /lyrics/:id
	// We check if the route definition *includes* an :id parameter. Hon<PERSON> doesn't easily expose
	// the matched route pattern here, so we check if c.req.param('id') exists.
	const id = c.req.param('id');

	// Only validate 'id' if the parameter actually exists for the matched route.
	// Routes without ':id' won't have `c.req.param('id')` return a value.
	// We also need to ensure the route *expects* an ID. A simple check is if the path includes '/lyrics/'
	// This is a bit fragile; ideally Hono would provide better route introspection in middleware.
	if (c.req.path.startsWith('/lyrics/') && id !== undefined) {
		// Check if ID is missing or empty after potentially being extracted
		if (!id) {
			console.log('Missing track ID in path parameter.');
			return c.json(
				{
					error: true,
					details: 'Invalid request: Track ID is missing in the URL path.',
					status: 400,
				},
				400
			);
		}

		// Validate the format of the ID (e.g., alphanumeric)
		// Spotify IDs are Base62, so alphanumeric is a good check.
		if (!/^[a-zA-Z0-9]+$/.test(id)) {
			console.log(`Invalid track ID format: ${id}`);
			return c.json(
				{
					error: true,
					details: 'Invalid request: Track ID format is invalid.',
					status: 400,
				},
				400
			);
		}
	}

	// If all checks pass, proceed to the next middleware or handler
	await next();
};
